
# 工程项目结构介绍
- 这是个昆明花卉拍卖系统,有前后端系统.前端目录:flower-auction-admin,后端目录:flower-auction
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 前端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/run.sh
- 原始需求: sell_flower.md
- 技术架构文档: docs目录下

# buglist
## 商品管理
- [] 分类管理,分类树页,点击删除按钮前端未向后端发起请求,问题原因:console里有点击事件但是二次确认框没有显示出来.

## 拍卖管理
- [] 拍卖会列表,编辑页,详情页里拍卖地点,拍卖师 无数据

- [] 拍卖商品-拍卖商品列表页没显示出数据 

- [] 竞价记录,多条件搜索:http://localhost:8081/api/v1/auctions?page=1&pageSize=100 接口没有把搜索条件带给后端,同时检查后端是否支持

- [] 实时竞价页,所有数据请使用后端api接口,请实现前后端逻辑

## 验证
- 修复后从前后端角度验证bug是否解决.

