
# 工程项目结构介绍
- 这是个昆明花卉拍卖系统,有前后端系统.前端目录:flower-auction-admin,后端目录:flower-auction
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 前端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/run.sh
- 原始需求: sell_flower.md
- 技术架构文档: docs目录下

# buglist
## 商品管理
- [] 分类管理,编辑分类页,点击更新,后端接口返回{"success":false,"message":"分类更新成功"},但是前端提示:操作失败：操作失败，请稍后重试.添加,删除请调api后端接口

## 拍卖管理
- [] 拍卖商品-添加拍卖商品.报400错误.http://localhost:8081/api/v1/auction-items 200 返回成功但是:操作失败：操作失败，请稍后重试

- [] 实时竞价,请使用后端api数据,完整的时间前后端逻辑
- [] 竞价记录,请使用后端api数据,完整的时间前后端逻辑

## 验证 
- 修复后从前后端角度验证bug是否解决.





