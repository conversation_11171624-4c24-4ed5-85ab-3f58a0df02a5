
# 工程项目结构介绍
- 这是个昆明花卉拍卖系统,有前后端系统.前端目录:flower-auction-admin,后端目录:flower-auction
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 前端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/run.sh
- 原始需求: sell_flower.md
- 技术架构文档: docs目录下

# buglist
## 商品管理
-[] 分类管理,分类树页,节点被选中后,编辑/添加/删除按钮就看不清了.请优化ui体验.![alt text](image.png)
- [] 分类管理,分类树页,编辑节点修改了分类描述,点击更新,分类更新成功,但是分类详情没有显示还是老数据

- [] 分类管理,分类树页,点击删除按钮前端没删除事件,因为从调试network里没看到有api被调用

## 拍卖管理
- [✅] 拍卖会列表,点击编辑按钮:Unexpected Application Error!
date.isValid is not a function
TypeError: date.isValid is not a function
    at Object.isValidate (http://localhost:3001/static/js/bundle.js:105318:17)
    at Object.current (http://localhost:3001/static/js/bundle.js:101821:21)
    at http://localhost:3001/static/js/bundle.js:124137:114
    at http://localhost:3001/static/js/bundle.js:101589:22
    at Array.map (<anonymous>)
    at http://localhost:3001/static/js/bundle.js:101571:30
    at mountMemo (http://localhost:3001/static/js/bundle.js:131375:21)
    at Object.useMemo (http://localhost:3001/static/js/bundle.js:139584:16)
    at exports.useMemo (http://localhost:3001/static/js/bundle.js:155253:32)
    at useFieldsInvalidate (http://localhost:3001/static/js/bundle.js:101570:62)

- [✅] 拍卖商品-添加拍卖商品后,列表页没显示数据

- [✅] 实时竞价,接口报500错误:http://localhost:8081/api/v1/bids/auction/1?page=1&pageSize=50 404 **已解决**
- [✅] 竞价记录,x

## 验证 
- 修复后从前后端角度验证bug是否解决.





