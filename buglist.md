
# 工程项目结构介绍
- 这是个昆明花卉拍卖系统,有前后端系统.前端目录:flower-auction-admin,后端目录:flower-auction
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 前端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/run.sh
- 原始需求: sell_flower.md
- 技术架构文档: docs目录下

# buglist
## 商品管理
- [] 分类管理,分类树页,删除按钮没有调后端接口啊

## 拍卖管理
- [] 拍卖会类别,按条件搜索不生效.接口没有实现多条件过滤
- [] 拍卖商品-添加拍卖商品.报400错误.http://localhost:8081/api/v1/auction-items 200 返回成功但是ui显示:操作失败：操作失败，请稍后重试

- [] 实时竞价,接口报404错误:http://localhost:8081/api/v1/bids/auction/1?page=1&pageSize=50 404
- [] 竞价记录,接口报错404错误: http://localhost:8081/api/v1/bids/auction/3?page=1&pageSize=50 404

## 验证 
- 修复后从前后端角度验证bug是否解决.





