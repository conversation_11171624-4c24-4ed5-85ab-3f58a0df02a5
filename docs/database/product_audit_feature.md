# 商品审核功能设计文档

## 1. 功能概述

商品审核功能是昆明花卉拍卖系统的重要组成部分，用于确保上架商品的质量和合规性。该功能支持多条件搜索、审核状态管理、审核历史追踪等特性。

## 2. 业务流程

### 2.1 商品审核流程
1. **商品提交**：供应商提交商品信息，状态为 `pending`（待审核）
2. **审核分配**：系统自动或手动分配给审核员
3. **审核处理**：审核员查看商品信息，做出审核决定
4. **审核结果**：
   - `approved`（通过）：商品可以参与拍卖
   - `rejected`（拒绝）：商品不能参与拍卖，需要修改后重新提交
5. **历史记录**：所有审核操作都会记录到审核历史表

### 2.2 审核状态转换
```
pending → approved  (审核通过)
pending → rejected  (审核拒绝)
rejected → pending  (重新提交审核)
```

## 3. 数据库设计

### 3.1 商品表字段扩展
在原有商品表基础上，新增以下审核相关字段：

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| audit_status | varchar(20) | 审核状态 | pending |
| audit_reason | text | 审核意见 | NULL |
| audit_time | datetime | 审核时间 | NULL |
| auditor_id | bigint(20) | 审核员ID | NULL |

### 3.2 审核历史表
新增 `product_audit_history` 表，记录所有审核操作：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| product_id | bigint(20) | 商品ID |
| audit_status | varchar(20) | 审核状态 |
| audit_reason | text | 审核意见 |
| auditor_id | bigint(20) | 审核员ID |
| audit_time | datetime | 审核时间 |
| created_at | datetime | 创建时间 |

### 3.3 索引设计
为提高查询性能，创建以下索引：
- `idx_audit_status`：审核状态索引
- `idx_audit_time`：审核时间索引
- `idx_auditor`：审核员索引
- `idx_product`：商品ID索引（审核历史表）

## 4. API设计

### 4.1 商品列表查询（支持审核筛选）
```
GET /api/v1/products
```

**查询参数：**
- `name`：商品名称（模糊搜索）
- `categoryId`：分类ID
- `qualityLevel`：质量等级
- `status`：商品状态
- `origin`：产地（模糊搜索）
- `auditStatus`：审核状态（新增）
- `supplierName`：供应商名称（新增）
- `page`：页码
- `pageSize`：每页数量

### 4.2 商品审核
```
POST /api/v1/products/{id}/audit
```

**请求体：**
```json
{
  "status": "approved|rejected",
  "reason": "审核意见"
}
```

**响应：**
```json
{
  "success": true,
  "message": "审核成功"
}
```

### 4.3 审核历史查询
```
GET /api/v1/products/{id}/audit-history
```

**响应：**
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "auditStatus": "approved",
      "auditReason": "商品质量符合要求",
      "auditorName": "张三",
      "auditTime": "2024-01-01 10:00:00"
    }
  ]
}
```

## 5. 前端功能

### 5.1 商品审核页面
- **搜索功能**：支持商品名称、审核状态、供应商名称搜索
- **列表展示**：显示商品基本信息和审核状态
- **审核操作**：快速通过/拒绝，详细审核
- **状态筛选**：按审核状态筛选商品

### 5.2 审核状态显示
- `pending`：橙色标签，显示"待审核"
- `approved`：绿色标签，显示"已通过"
- `rejected`：红色标签，显示"已拒绝"

## 6. 技术实现

### 6.1 后端实现要点
1. **参数验证**：审核状态只能是 approved 或 rejected
2. **权限控制**：只有审核员角色可以执行审核操作
3. **事务处理**：审核操作和历史记录插入在同一事务中
4. **并发控制**：防止同一商品被多个审核员同时审核

### 6.2 前端实现要点
1. **状态同步**：审核后立即刷新列表
2. **错误处理**：统一的错误提示机制
3. **用户体验**：加载状态、操作反馈
4. **权限控制**：根据用户角色显示不同操作按钮

## 7. 性能优化

### 7.1 数据库优化
- 合理使用索引，提高查询效率
- 审核历史表按月分表，控制单表数据量
- 定期清理过期的审核历史数据

### 7.2 缓存策略
- 审核员信息缓存
- 商品分类信息缓存
- 热点商品审核状态缓存

## 8. 监控和统计

### 8.1 审核效率统计
- 每日审核数量
- 审核员工作量统计
- 平均审核时间
- 审核通过率

### 8.2 业务监控
- 待审核商品数量告警
- 审核超时提醒
- 审核质量监控

## 9. 安全考虑

### 9.1 权限控制
- 只有具备审核权限的用户才能执行审核操作
- 审核员不能审核自己提交的商品
- 审核操作需要记录操作日志

### 9.2 数据完整性
- 审核状态变更必须有审核意见
- 审核时间自动记录，不允许手动修改
- 审核历史数据不允许删除或修改

## 10. 部署说明

### 10.1 数据库迁移
1. 执行 `V2__add_product_audit_fields.sql` 迁移脚本
2. 验证字段和索引创建成功
3. 更新现有商品数据的审核状态

### 10.2 应用部署
1. 更新后端代码，重启服务
2. 更新前端代码，重新构建
3. 验证审核功能正常工作

## 11. 测试用例

### 11.1 功能测试
- 商品审核状态搜索
- 供应商名称搜索
- 审核操作（通过/拒绝）
- 审核历史查询

### 11.2 性能测试
- 大量商品数据下的搜索性能
- 并发审核操作测试
- 审核历史查询性能

### 11.3 安全测试
- 权限验证测试
- 数据完整性测试
- 操作日志记录测试
