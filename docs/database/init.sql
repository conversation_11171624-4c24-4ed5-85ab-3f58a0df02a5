-- 创建数据库
CREATE DATABASE IF NOT EXISTS `user_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `product_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `auction_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
CREATE DATABASE IF NOT EXISTS `order_db` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 切换到用户数据库
USE `user_db`;

-- 创建用户表
CREATE TABLE IF NOT EXISTS `user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `phone` varchar(20) NOT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `user_type` tinyint(4) NOT NULL COMMENT '用户类型：1-拍卖师 2-买家 3-管理员 4-质检员',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_user_type` (`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 创建角色表
CREATE TABLE IF NOT EXISTS `role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(50) NOT NULL COMMENT '角色名称',
    `code` varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 创建用户角色关联表
CREATE TABLE IF NOT EXISTS `user_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 切换到商品数据库
USE `product_db`;

-- 创建商品表
CREATE TABLE IF NOT EXISTS `product` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `name` varchar(100) NOT NULL COMMENT '商品名称',
    `category_id` bigint(20) NOT NULL COMMENT '类别ID',
    `description` text COMMENT '商品描述',
    `quality_level` tinyint(4) NOT NULL COMMENT '品质等级：1-优 2-良 3-中',
    `origin` varchar(100) NOT NULL COMMENT '产地',
    `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-下架 1-上架',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category_id`),
    KEY `idx_supplier` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';

-- 创建商品类别表
CREATE TABLE IF NOT EXISTS `category` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类别ID',
    `name` varchar(50) NOT NULL COMMENT '类别名称',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父类别ID',
    `level` tinyint(4) NOT NULL COMMENT '层级',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品类别表';

-- 切换到拍卖数据库
USE `auction_db`;

-- 创建拍卖会表
CREATE TABLE IF NOT EXISTS `auction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖会ID',
    `name` varchar(100) NOT NULL COMMENT '拍卖会名称',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime NOT NULL COMMENT '结束时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已结束',
    `auctioneer_id` bigint(20) NOT NULL COMMENT '拍卖师ID',
    `description` text COMMENT '拍卖会描述',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auctioneer` (`auctioneer_id`),
    KEY `idx_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖会表';

-- 创建拍卖商品表
CREATE TABLE IF NOT EXISTS `auction_item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖商品ID',
    `auction_id` bigint(20) NOT NULL COMMENT '拍卖会ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `start_price` decimal(10,2) NOT NULL COMMENT '起拍价',
    `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
    `step_price` decimal(10,2) NOT NULL COMMENT '加价幅度',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已成交 3-流拍',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `winner_id` bigint(20) DEFAULT NULL COMMENT '中标用户ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction` (`auction_id`),
    KEY `idx_product` (`product_id`),
    KEY `idx_winner` (`winner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖商品表';

-- 创建竞价记录表（按月分表）
CREATE TABLE IF NOT EXISTS `bid_202505` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '竞价ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `price` decimal(10,2) NOT NULL COMMENT '出价',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-有效 0-无效',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction_item` (`auction_item_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_create_time` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞价记录表202505';

-- 切换到订单数据库
USE `order_db`;

-- 创建订单表（按用户ID范围分表）
CREATE TABLE IF NOT EXISTS `order_0` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_no` varchar(32) NOT NULL COMMENT '订单编号',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user` (`user_id`),
    KEY `idx_auction_item` (`auction_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表0';

-- 创建支付记录表（按月分表）
CREATE TABLE IF NOT EXISTS `payment_202505` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `payment_no` varchar(64) NOT NULL COMMENT '支付流水号',
    `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
    `payment_method` tinyint(4) NOT NULL COMMENT '支付方式：1-支付宝 2-微信 3-银行卡',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付 1-支付成功 2-支付失败',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    KEY `idx_order` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表202401';

-- 初始化基础数据
USE `user_db`;

-- 插入角色数据
INSERT INTO `role` (`name`, `code`, `description`) VALUES
('系统管理员', 'ADMIN', '系统管理员角色'),
('拍卖师', 'AUCTIONEER', '拍卖师角色'),
('买家', 'BUYER', '买家角色'),
('质检员', 'QUALITY_INSPECTOR', '质检员角色');

-- 插入管理员用户
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`, `created_at`) VALUES
('admin', '$2a$10$YE0zwbpm/yqiVEONVs60XukNWLHce5fqA3wGOg5LZ4W103FwIbPum', '系统管理员', '13800000000', '<EMAIL>', 3, 1, '2025-05-30');

-- 插入管理员角色关联
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'admin' AND r.code = 'ADMIN';

USE `product_db`;

-- 插入商品类别数据
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('鲜花', NULL, 1, 1),
('绿植', NULL, 1, 2),
('玫瑰', 1, 2, 1),
('百合', 1, 2, 2),
('康乃馨', 1, 2, 3),
('盆栽', 2, 2, 1),
('观叶植物', 2, 2, 2); 