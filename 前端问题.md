# 前端问题
## 工程介绍
- 这是个前后端花卉拍卖系统,前端目录:flower-auction-admin,后端目录:flower-auction
- 后端启动脚步: /Users/<USER>/Documents/mycode/leishengjie/flower-auction/run.sh
- 

## 商品管理
- [] 分类管理,请你分类树使用树形UI,+/-展开图标,点击图标可展开或收起子分类.删除分类菜单请调api实现删除分类菜单.

## 拍卖管理
- [] 拍卖会列表-新增拍卖会,拍卖师项暂无数据导致无法新增拍卖会.请从后端获取真实的拍卖师数据.
![alt text](image.png)
- [] 拍卖商品-添加拍卖商品.报400错误.http://localhost:8081/api/v1/auction-items 400 
{
    "success": false,
    "error": "Key: 'AddAuctionItemRequest.StartPrice' Error:Field validation for 'StartPrice' failed on the 'required' tag\nKey: 'AddAuctionItemRequest.StepPrice' Error:Field validation for 'StepPrice' failed on the 'required' tag\nKey: 'AddAuctionItemRequest.StartTime' Error:Field validation for 'StartTime' failed on the 'required' tag",
    "message": ""
}

- [] 实时竞价,请使用api真实数据,
- [] 竞价记录,请使用后端api返回的真实数据

注意: 保证前端,后端逻辑连通互动





