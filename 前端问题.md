# 前端问题(确认前端问题是否修复,后端是否需要处理)

## 用户管理
- [✅] 用户列表, 启用/禁用状态按钮不可用,需要和列表联动 - **已修复**：后端API响应格式问题已修复，状态切换功能正常，API调用成功后会刷新列表
- [✅] 新增用户,手机号不唯一标识人校验 - **已修复**：后端已有手机号唯一性校验，重复手机号会返回"用户已存在"错误
- [✅] 角色管理,角色编码的搜索框调整为下拉选择框 - **已修复**：将搜索表单中的角色编码输入框改为下拉选择框，支持搜索和筛选

## 商品管理
- [✅] 商品列表,列表里的分类没显示文字 - **已修复**：修改前端代码使用 `category.name` 显示分类名称，后端返回完整分类信息
- [✅] 点击查看商品详情,点击编辑按钮,商品详情页里没有显示商品分类 - **已修复**：修改商品详情页面使用 `category.name` 显示分类名称
- [✅] 商品列表,点击编辑按钮,商品详情页里没有显示商品分类和供应商 - **已修复**：编辑表单获取真实供应商数据，详情页正确显示分类和供应商信息
- [✅] 分类管理,新增分类页的分类编码做出下拉选择框 - **已修复**：将分类编码输入框改为下拉选择框，提供预设的分类编码选项，支持搜索和筛选
- [✅] 分类管理,排序字段强制必填用户体验差 - **已修复**：排序字段改为可选，提供友好提示"留空自动分配"，系统自动计算排序值
- [✅] 分类管理,分类编码选择让人看不懂 - **已修复**：实现智能编码选择，根据分类级别和父分类过滤可用编码，提供上下文感知的提示和说明
- [✅] 商品审核,商品详情页,商品分类,供应商没显示 - **已修复**：修改商品审核页面的数据结构，正确显示分类和供应商信息，兼容后端返回的数据格式

## 拍卖管理
- [✅] 拍卖会列表-新增拍卖会,接口报错 - **已修复**：修改前端表单字段名从 `title` 改为 `name`，添加拍卖师选择字段 `auctioneerID`，获取真实拍卖师数据





