# 昆明花卉拍卖系统功能清单

## 拍卖师端

| 功能模块       | 功能描述                                                                                                     |
|----------------|--------------------------------------------------------------------------------------------------------------|
| 注册登录       | 1. 账号验证：使用工号+密码登录，对接HR系统校验身份有效性，支持指纹识别<br>2. 多端同步：支持PC端与移动端登录，自动同步拍卖员角色权限<br>3. 安全审计：登录记录实时写入日志系统，异常登录触发短信预警<br>4. 权限初始化：首次登录自动绑定负责的钟号范围，可申请调整权限范围 |
| 批次全生命周期管理 | 1. 批次创建：手动或导入供货批次信息，生成唯一拍卖批次号，关联仓储库位<br>2. 状态跟踪：实时显示批次状态（待起拍、竞拍中、已成交、流拍），支持批量修改批次属性<br>3. 信息管理：查看批次对应的供货商、质检报告、购买商关注数据，标注重点批次<br>4. 归档管理：自动归档已结束批次（保留3年数据），支持按钟号、品类快速检索历史批次 |
| 起拍           | 1. 参数设置：配置起拍价、加价幅度、转速、最小购买量，支持预设模板快速应用<br>2. 规则校验：系统自动验证参数合法性，提示冲突项<br>3. 预加载机制：提前10分钟加载待起拍批次信息至缓存<br>4. 多钟并行：支持同时配置多个钟号的起拍参数<br>5. 信号广播：起拍指令通过消息队列广播至购买商端、投屏端<br>6. 状态更新：核心竞价模块接收起拍信号后，标记钟号为"竞拍中"<br>7. 日志记录：记录起拍时间、拍卖员账号、参数详情至操作日志<br>8. 异常处理：起拍失败时自动重试3次，失败后生成异常工单 |
| 流拍           | 1. 触发条件：拍卖员点击"流拍"按钮，选择流拍原因<br>2. 流程执行：系统终止当前钟号竞价，释放库存并标记批次为"流拍待处理"<br>3. 后续处理：流拍批次自动转入二次拍卖池或转为线下交易<br>4. 数据同步：流拍结果同步至结算系统、库存系统<br>5. 条件监测：超过设定时间无有效出价时自动触发流拍<br>6. 阈值配置：支持自定义流拍等待时间<br>7. 无人干预：自动流拍无需拍卖员操作<br>8. 防误触机制：自动流拍前5秒弹窗提示确认 |
| 控价           | 1. 手动降低当前价格刺激出价<br>2. 设定最低成交价<br>3. 停拍功能处理突发情况<br>4. 调整钟号转动速度控制拍卖节奏 |
| 成交           | 1. 确定最高价购买商<br>2. 处理埋单交易并生成分货单                                                          |
| 历史数据       | 按日期、钟号、品类等筛选历史拍卖记录                                                                         |

## 购买商端

| 功能模块       | 功能描述                                                                                                     |
|----------------|--------------------------------------------------------------------------------------------------------------|
| 登录注册       | 1. 注册：支持手机号/邮箱注册，需短信验证码验证<br>2. 登录：支持密码登录、指纹/面容识别、第三方快捷登录<br>3. 安全校验：异地登录触发二次验证<br>4. 权限初始化：自动分配"购买商"角色 |
| 供货批次模块   | 1. 批次浏览：按品类、等级、拍卖时间筛选<br>2. 批次详情：查看品种、数量、起拍价等信息<br>3. 快捷操作：一键关注批次、提交埋单 |
| 拍卖模块       | 1. 实时竞价：动态显示当前价格、转速等信息<br>2. 一键出价：发送抢拍请求并自动校验<br>3. 结果反馈：成功/失败即时提示<br>4. 高并发优化：支持每秒千级并发<br>5-8. 埋单功能管理<br>9-12. 关注与提醒功能<br>13-15. 历史查询与导出 |
| 员工管理       | 1-4. 白名单管理<br>5-8. 角色与权限管理                                                                       |
| 账户管理       | 1-4. 充值功能<br>5-8. 提现功能<br>9-12. 交易台账<br>13-16. 资金流水管理                                      |

## 投屏端

| 功能模块       | 功能描述                                                                                                     |
|----------------|--------------------------------------------------------------------------------------------------------------|
| 实时数据展示   | 1. 显示待拍批次信息<br>2. 显示当前钟号实时数据<br>3. 显示成交记录                                            |
| 多屏布局管理   | 支持自定义布局和多区域展示                                                                                   |
| 异常处理       | 1. 断网时自动加载缓存<br>2. 显示系统告警                                                                     |

## 管理后台

| 功能模块       | 功能描述                                                                                                     |
|----------------|--------------------------------------------------------------------------------------------------------------|
| 用户管理       | 1. 账号全生命周期管理<br>2. 实名认证管理<br>3. 子账号管理                                                    |
| 权限管理       | 1. 角色矩阵配置<br>2. 操作审批流<br>3. 审计日志                                                              |
| 商品基础管理   | 1. 维护品类编码和品种信息<br>2. 配置质检规则和拍卖参数<br>3. 定义包装单位和容量                               |
| 拍卖流程管理   | 1. 查看和修改批次状态<br>2. 手动干预拍卖<br>3. 统计分析功能<br>4. 流拍批次处理<br>5-8. 数据追溯与分析功能     |
| 财务模块       | 1-4. 数据追溯与分析功能<br>5-8. 资金和税务管理                                                               |


## 品控端
### 注册登录
1. **注册**  
   - 支持工号 + 手机号注册（需 HR 系统校验工号有效性）
2. **登录**  
   - 支持密码登录、指纹登录、短信验证码登录
3. **安全验证**  
   - 登录异常（如非工作时间登录）触发二次验证（企业微信通知）
4. **权限同步**  
   - 注册后自动分配 "品控员" 角色（权限包括批次录入、质检降级、数量核验），同步至权限管理系统

### 品控管理
1. **数据获取**  
   - 通过扫码枪扫描批次条码（或手动输入批次号）自动同步供货商端提交的批次信息（品种、申报数量、规格）
2. **信息校验**  
   - 核对系统信息与实物标签一致性（如品种是否匹配），标记异常（如 "标签缺失"）
3. **状态更新**  
   - 录入完成后将批次状态标记为 "待质检"，同步至库存系统和竞拍系统
4. **补录支持**  
   - 对无条码批次手动录入基础信息（需上传手写记录照片作为凭证）
5. **等级判定**  
   - 根据质检标准（如瑕疵率＞5% 降级）自动推荐等级，支持手动调整（如从 A 级降至 B 级）
6. **原因记录**  
   - 必填降级原因（如 "压伤率过高"），支持选择预设原因或自定义输入
7. **凭证上传**  
   - 上传质检照片/视频（如瑕疵部位特写），关联至批次档案
8. **系统同步**  
   - 降级结果同步至库存系统（更新可售等级）、竞拍系统（调整起拍价）、供货商端（通知降级信息）
9. **复核机制**  
   - 降级等级为 C 级及以下时需上级品控员复核（通过系统自动推送待办任务）
10. **数量核对**  
    - 现场清点实物数量，与供货商申报数量比对（如申报 200 桶，实际 195 桶）
11. **差异处理**  
    - 短缺（＜申报量）：标记 "短缺 5 桶"，生成短缺报告（需供货商确认）
    - 溢余（＞申报量）：标记 "溢余 3 桶"，同步至库存系统作为额外可售批次
12. **系统同步**  
    - 核验结果同步至库存系统（更新实际可售数量）、结算系统（短缺部分扣减供货商结算金额）

## 拍前业务
### 商品管理
1. 建立花卉品类（玫瑰/百合）、品种（卡罗拉/苏醒）编码规则，支持自定义色系（红/粉）、长度（30cm/40cm）等标准，关联仓储和拍卖钟参数
2. 定义压伤、折枝、褪色等瑕疵代码，支持质检人员通过品控 APP 现场录入瑕疵等级，自动计算供货质量评分

### 拍卖管理
1. 支持 Excel 导入供货信息（批次号、品种、数量），自动校验数据格式，异常数据标记并通知供货商
2. 记录购买商埋单历史、关注偏好，分析热门品类，辅助拍卖师调整控价策略

## 拍后业务
### 结算管理
1. 根据交易金额自动计算供货商分成（85%）、平台佣金（5%）、物流费用（10%），支持按批次生成清分报表
2. 成交后自动生成电子发票（含商品明细、金额），支持购买商在线申请下载，对接税务系统

### 售后管理
- 购买商可通过 APP、后台提交质量投诉，系统自动分配工单至质检部门，跟踪处理进度并通知用户

## 竞价模块
### 竞价算法
1. 从购买商抢拍信号中提取钟号、桶数、步长值（如每桶加价幅度）、出价时间戳等参数，支持自定义步长单位（元/桶、元/枝）
2. 根据拍卖类型（增价/减价）计算有效出价区间（如起拍价 10 元，步长 2 元时，有效价为 10/12/14 元），过滤无效出价（如低于起拍价）
3. 对同一钟号的抢拍请求按价格降序、时间升序排序，确保最高出价且最先提交的购买商中标
4. 采用 Redis 锁确保同一批次商品在高并发下仅被一个购买商成交，避免超卖（如剩余 5 桶时拒绝第 6 笔成交）

### 路由处理
- 建立拍卖师与钟号的绑定关系（如拍卖师 A 负责钟号 1-3），确保抢拍信号精准路由至对应拍卖师终端

### 信号管理
1. 拍卖师可选择手动点击起拍或设置定时自动起拍（如每天 9:00 自动启动钟号 1），支持预加载供货批次信息
2. 当拍卖钟转动超过设定时间（如 5 分钟）无有效出价时，系统自动触发流拍并通知拍卖师
3. 拍卖师可在拍卖中手动调整当前价格（如临时降价 10%）、暂停钟号转动，触发二次竞价

### 系统监控
1. 实时显示拍卖师/购买商终端连接状态（绿色=在线，灰色=离线），统计各钟号当前参与人数
2. 当单一 IP 地址抢拍请求超过阈值（如 100 次/分钟）时，触发预警并自动封禁 IP，生成报警日志
3. **缓存策略管理**  
   - 全量缓存：每日凌晨通过 DataWorks 从交易库（RDS MySQL）全量同步基础数据（如品类编码、供货商信息）至 OSS（冷存储），生成快照（如 "category_20250520.json"）
   - 增量缓存：通过 DTS 订阅交易库 Binlog，实时捕获数据变更（如新品类创建），增量更新 OSS 缓存（延迟≤5 秒）
4. **缓存更新机制**  
   - 定时刷新：基础数据每日刷新 1 次，高频变更数据（如拍卖钟参数）每小时刷新
   - 事件触发：当管理后台修改品类规则时，自动触发 OSS 缓存失效并重新加载
5. **数据一致性保障**  
   - 版本控制：OSS 对象版本号自动递增，支持回滚至历史版本（如 "恢复 2025-05-19 的品类数据"）
   - 校验机制：下载缓存时校验 MD5 哈希值，确保数据未被篡改（如哈希值不一致时触发重新拉取）
6. **缓存使用监控**  
   - 命中率统计：通过 CloudMonitor 监控缓存命中率（如 "品类数据命中率 95%"），低于 80% 时报警
   - 存储成本优化：自动清理 7 天未访问的冷数据，归档至 OSS 低频存储（降低 50% 存储成本）

### 配置管理
- 管理员通过后台界面配置不同品类的竞价规则（如玫瑰采用增价拍卖，百合采用荷兰式拍卖），支持一键应用至指定钟号

## 数据服务
### 历史数据
1. 多源数据抽取：从竞拍系统、结算系统、库存系统等业务系统抽取历史交易数据、操作日志及基础档案
2. 清洗转换：通过 ETL 工具清洗重复记录、补全缺失值（如流拍原因）、统一编码规则（如品类代码标准化）
3. 归档存储：将处理后的数据按主题（如拍卖、用户、物流）归档至数据仓库，支持冷数据转存至对象存储（如 S3），支持按年/月快速检索
4. 异常监控：记录数据同步失败案例（如字段类型不匹配），自动重试并生成审计报告

### 实时数据
1. 低延迟通道：通过 Kafka 消息队列构建实时数据管道，捕获竞拍价格变动、库存扣减等事件，延迟≤50ms
2. 跨系统联动：同步竞拍状态至投屏端（LED 大屏）、购买商 APP 及管理后台，确保多端数据一致（如成交后库存、资金状态同步更新）
3. 外部接口：提供 WebSocket 订阅接口，供第三方系统（如电商平台、物流系统）实时获取拍卖动态（如当前价格、剩余数量）
4. 事务保障：通过分布式事务机制确保跨系统操作一致性（如成交时同时冻结资金和锁定库存）

### 数据看板
1. 多维度分析：提供实时成交看板（如 "今日成交量 TOP10 品类"）、历史趋势分析（如近 3 个月流拍率波动），支持钻取查询（如从品类下钻至具体批次）
2. 可视化组件：集成 ECharts 实现价格波动曲线、热力图、漏斗图，支持自定义仪表盘布局（如拍卖师效率看板）
3. 权限隔离：按角色展示数据（如拍卖师仅见负责钟号数据，管理员可见全量数据），敏感字段脱敏（如隐藏购买商手机号）
4. 数据导出：支持 Excel/PDF 导出报表，对接打印系统生成纸质报告（如周度拍卖分析报告）

### 中间件消息队列
1. 处理高并发竞价请求，按钟号、拍卖师优先级排队，确保核心模块处理效率（队列积压≤1000 笔/秒）
2. 协调竞价服务、数据服务、支付系统之间的逻辑交互，如成交后触发资金冻结和物流分配

### API接口
1. 提供第三方系统/用户的身份认证服务，支持 OAuth2.0、JWT 令牌
2. 查询用户在系统内的操作权限（如竞拍钟号、埋单额度）
3. 提供拍品基础信息（品种、等级、质检报告）的查询服务
4. 支持外部系统触发竞拍动作（如抢拍、埋单、撤单）
5. 外部系统确认成交结果（如电商平台同步订单）
6. 提供交易资金流水、佣金、保证金的对账服务
7. 提供实时/历史成交数据（价格、成交量、流拍率），用于市场分析
8. 查询拍品库存动态（可售数量、已锁定数量、已成交数量）
9. 提供拍品质检报告（瑕疵率、等级）的只读查询服务

## 日志系统
### 多端操作日志
1. 供货批次创建/修改/删除、质检报告提交、资金流水查询
2. 批次审核状态、库存锁定结果、价格同步状态
3. 与品控端/拍卖后台的数据同步时间、接口调用记录
4. 质检开始/结束、瑕疵等级录入、质检报告确认
5. 扫码枪/称重设备/图像采集设备的交互记录、异常报警
6. 供货商申诉记录、质检复核结果
7. 抢拍/埋单/撤单、关注批次、交易员权限调整
8. 保证金充值/冻结/解冻、佣金扣除、退款申请
9. 登录设备类型、IP 地址、网络延迟
10. 主账号分配/回收钟号权限、操作审批记录
11. 交易员间消息记录、抢拍策略调整
12. 起拍/停拍/流拍/控价、钟号切换、成交确认
13. 电子钟同步记录、成交凭证打印时间
14. 人工终止竞价、处理异常出价 IP
15. 拍卖钟参数修改、会员等级规则调整、角色权限管理
16. 历史数据删除、区块链存证查询、报表导出
17. 核心模块性能指标、故障记录、集群切换记录

### 系统日志
1. **流程日志**：记录起拍、流拍、控价、成交全流程操作，包含操作时间、拍卖师账号、钟号、参数详情
2. **竞价记录**：存储每笔抢拍/埋单请求的购买商 ID、出价金额、时间戳、响应状态
3. **系统状态**：监控钟号运行状态（竞拍中/暂停/结束）、并发请求量（如峰值 1000 笔/秒）、分布式锁使用情况（如 Redis 锁获取耗时）
4. **异常处理**：记录无效出价、防刷量封禁 IP（如 IP ************* 因 100 次/分钟请求被封禁）、算法异常（如优胜者计算错误）
5. **资金操作**：记录保证金充值/冻结/解冻、佣金扣除、分账结果，包含交易流水号、金额、支付渠道（支付宝/银联）、用户 ID
6. **接口交互**：存储与第三方支付平台的请求/响应日志，记录订单号、支付状态（成功/失败/处理中）
7. **异常监控**：捕获支付超时、金额对账不符（如平台计算佣金与银行记录差异）、洗钱风险预警（如大额异常充值）
8. **合规记录**：记录身份验证日志（如购买商实名认证时间）、敏感操作审计（如手动调整佣金比例）
9. **清分计算**：记录分账规则应用（如供货商 85%、平台 5%、物流 10%）、计算过程（如成交金额 10000 元→供货商 8500 元）
10. **凭证生成**：存储电子发票开具记录（发票号、金额、开票时间）、结算单导出日志（用户 ID、导出时间）
11. **对账记录**：记录与银行/第三方支付的每日对账结果（如 "资金流水匹配率 99.9%"）、差异处理日志（如手动调账原因）
12. **售后关联**：记录售后订单的资金回退（如退货退款金额、原成交单号）、违约金扣除（如延迟提货罚款）

## 权限系统
### 统一认证体系
- 支持拍卖师、购买商、供货商通过同一账号体系登录多端（PC/APP/大屏），对接 CA 认证实现统一身份核验

### 角色权限配置
1. 供货商端
2. 品控端
3. 购买商端
4. 交易员端
5. 拍卖师端
6. 管理后台

## 数据库
### 数据库构建
1. 交易模型
2. 分析模型
3. 分区分表
4. 索引策略
5. 历史数据
6. 默认参数

### 数据安全设计
1. 敏感字段加密
2. 行级权限加密

### 数据备份与恢复
1. 全量备份策略
2. 增量备份策略

### 数据库选型部署
1. 部署架构选型
2. 高可用性配置
3. 部署环境适配
