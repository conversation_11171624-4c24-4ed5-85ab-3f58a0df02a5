# 新手开发文档：添加搜索功能完整指南

## 📖 文档说明
本文档以"角色模块添加搜索功能"为例，详细说明前后端开发的完整流程，适合前后端开发新手学习。

---

## 🎯 需求分析

### 功能需求
在角色管理页面添加搜索功能，用户可以：
- 按角色名称搜索
- 按角色编码搜索
- 按角色状态筛选
- 重置搜索条件

### 技术要求
- 前端：React + TypeScript + Ant Design
- 后端：Go + Gin + GORM
- 数据库：MySQL

---

## 🔧 后端开发流程

### 1. 数据库设计

首先确认角色表结构：
```sql
CREATE TABLE roles (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '角色名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    status TINYINT DEFAULT 1 COMMENT '状态：1启用，0禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 定义数据模型

**文件位置**: `flower-auction/internal/model/role.go`

```go
package model

import (
    "time"
    "gorm.io/gorm"
)

// Role 角色模型
type Role struct {
    ID          uint           `json:"id" gorm:"primarykey"`
    Name        string         `json:"name" gorm:"size:100;not null;comment:角色名称"`
    Code        string         `json:"code" gorm:"size:50;not null;uniqueIndex;comment:角色编码"`
    Description string         `json:"description" gorm:"type:text;comment:角色描述"`
    Status      int            `json:"status" gorm:"default:1;comment:状态：1启用，0禁用"`
    CreatedAt   time.Time      `json:"created_at"`
    UpdatedAt   time.Time      `json:"updated_at"`
    DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (Role) TableName() string {
    return "roles"
}
```

### 3. 定义请求参数结构

**文件位置**: `flower-auction/internal/api/role.go`

```go
// RoleQueryParams 角色查询参数
type RoleQueryParams struct {
    Name     string `form:"name" json:"name"`         // 角色名称
    Code     string `form:"code" json:"code"`         // 角色编码
    Status   *int   `form:"status" json:"status"`     // 状态（使用指针区分0和未设置）
    Page     int    `form:"page" json:"page"`         // 页码
    PageSize int    `form:"pageSize" json:"pageSize"` // 每页数量
}
```

### 4. 实现Service层逻辑

**文件位置**: `flower-auction/internal/service/role_service.go`

```go
package service

import (
    "context"
    "strings"
    "your-project/internal/model"
    "gorm.io/gorm"
)

type RoleService struct {
    db *gorm.DB
}

func NewRoleService(db *gorm.DB) *RoleService {
    return &RoleService{db: db}
}

// GetRoleList 获取角色列表（带搜索）
func (s *RoleService) GetRoleList(ctx context.Context, params RoleQueryParams) ([]model.Role, int64, error) {
    var roles []model.Role
    var total int64

    // 构建查询条件
    query := s.db.Model(&model.Role{})

    // 按名称搜索（模糊匹配）
    if params.Name != "" {
        query = query.Where("name LIKE ?", "%"+strings.TrimSpace(params.Name)+"%")
    }

    // 按编码搜索（模糊匹配）
    if params.Code != "" {
        query = query.Where("code LIKE ?", "%"+strings.TrimSpace(params.Code)+"%")
    }

    // 按状态筛选
    if params.Status != nil {
        query = query.Where("status = ?", *params.Status)
    }

    // 获取总数
    if err := query.Count(&total).Error; err != nil {
        return nil, 0, err
    }

    // 分页查询
    offset := (params.Page - 1) * params.PageSize
    if err := query.Offset(offset).Limit(params.PageSize).
        Order("created_at DESC").Find(&roles).Error; err != nil {
        return nil, 0, err
    }

    return roles, total, nil
}
```

### 5. 实现API层

**文件位置**: `flower-auction/internal/api/role.go`

```go
package api

import (
    "net/http"
    "strconv"
    "github.com/gin-gonic/gin"
    "your-project/internal/service"
)

type RoleHandler struct {
    roleService *service.RoleService
}

func NewRoleHandler(roleService *service.RoleService) *RoleHandler {
    return &RoleHandler{roleService: roleService}
}

// GetRoleList 获取角色列表
func (h *RoleHandler) GetRoleList(c *gin.Context) {
    var params RoleQueryParams

    // 绑定查询参数
    if err := c.ShouldBindQuery(&params); err != nil {
        c.JSON(http.StatusBadRequest, gin.H{
            "success": false,
            "message": "参数格式错误",
            "error":   err.Error(),
        })
        return
    }

    // 设置默认值
    if params.Page <= 0 {
        params.Page = 1
    }
    if params.PageSize <= 0 {
        params.PageSize = 10
    }

    // 调用服务层
    roles, total, err := h.roleService.GetRoleList(c.Request.Context(), params)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{
            "success": false,
            "message": "获取角色列表失败",
            "error":   err.Error(),
        })
        return
    }

    // 返回结果
    c.JSON(http.StatusOK, gin.H{
        "success": true,
        "data": gin.H{
            "list":     roles,
            "total":    total,
            "page":     params.Page,
            "pageSize": params.PageSize,
        },
        "message": "获取成功",
    })
}
```

### 6. 注册路由

**文件位置**: `flower-auction/internal/router/router.go`

```go
package router

import (
    "github.com/gin-gonic/gin"
    "your-project/internal/api"
)

func SetupRouter(roleHandler *api.RoleHandler) *gin.Engine {
    r := gin.Default()

    // API路由组
    v1 := r.Group("/api/v1")
    {
        // 角色相关路由
        roles := v1.Group("/roles")
        {
            roles.GET("", roleHandler.GetRoleList)      // GET /api/v1/roles
            // 其他角色相关路由...
        }
    }

    return r
}
```

---

## 🎨 前端开发流程

### 1. 定义TypeScript接口

**文件位置**: `flower-auction-admin/src/types/role.ts`

```typescript
// 角色数据接口
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
export interface RoleQueryParams {
  name?: string;
  code?: string;
  status?: number;
  page: number;
  pageSize: number;
}

// API响应接口
export interface RoleListResponse {
  list: Role[];
  total: number;
  page: number;
  pageSize: number;
}
```

### 2. 实现API服务

**文件位置**: `flower-auction-admin/src/services/roleService.ts`

```typescript
import { apiClient } from './apiClient';
import { Role, RoleQueryParams, RoleListResponse } from '../types/role';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export const roleService = {
  // 获取角色列表（带搜索）
  getRoleList: async (params: RoleQueryParams): Promise<ApiResponse<RoleListResponse>> => {
    try {
      const response = await apiClient.get('/roles', { params });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      const errorMessage = error.response?.data?.error ||
                          error.response?.data?.message ||
                          '获取角色列表失败';
      return {
        success: false,
        data: { list: [], total: 0, page: 1, pageSize: 10 },
        message: errorMessage,
      };
    }
  },
};

### 3. 实现React组件

**文件位置**: `flower-auction-admin/src/pages/Users/<USER>/index.tsx`

```typescript
import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Form,
  message,
  Row,
  Col,
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { roleService } from '../../../services/roleService';
import { Role, RoleQueryParams } from '../../../types/role';

const { Option } = Select;

const RoleManagement: React.FC = () => {
  // 状态定义
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<RoleQueryParams>({
    page: 1,
    pageSize: 10,
  });

  // 表单实例
  const [searchForm] = Form.useForm();

  // 获取角色列表
  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await roleService.getRoleList(queryParams);
      if (response.success) {
        setRoles(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取角色列表失败');
        setRoles([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
      setRoles([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 监听查询参数变化，自动刷新数据
  useEffect(() => {
    fetchRoles();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    // 过滤空值
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);

    setQueryParams({
      ...queryParams,
      ...filteredValues,
      page: 1, // 搜索时重置到第一页
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: queryParams.pageSize,
    });
  };

  // 表格列定义
  const columns: ColumnsType<Role> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => (
        <span style={{ color: status === 1 ? 'green' : 'red' }}>
          {status === 1 ? '启用' : '禁用'}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
  ];

  return (
    <div className="role-management-container">
      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="角色名称">
                <Input
                  placeholder="请输入角色名称"
                  allowClear
                  onPressEnter={() => searchForm.submit()}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="code" label="角色编码">
                <Input
                  placeholder="请输入角色编码"
                  allowClear
                  onPressEnter={() => searchForm.submit()}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    htmlType="submit"
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={handleReset}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card>
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {/* 添加角色逻辑 */}}
            >
              新增角色
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRoles}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>

        {/* 角色列表表格 */}
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>
    </div>
  );
};

export default RoleManagement;
```

---

## 🧪 测试验证

### 1. 后端API测试

使用curl命令测试API：

```bash
# 测试基本查询
curl "http://localhost:8081/api/v1/roles?page=1&pageSize=10"

# 测试按名称搜索
curl "http://localhost:8081/api/v1/roles?name=管理员&page=1&pageSize=10"

# 测试按编码搜索
curl "http://localhost:8081/api/v1/roles?code=ADMIN&page=1&pageSize=10"

# 测试按状态筛选
curl "http://localhost:8081/api/v1/roles?status=1&page=1&pageSize=10"

# 测试组合搜索
curl "http://localhost:8081/api/v1/roles?name=管理&status=1&page=1&pageSize=10"
```

### 2. 前端功能测试

1. **打开角色管理页面**
   - 访问 `http://localhost:3001/users/roles`
   - 检查页面是否正常加载

2. **测试搜索功能**
   - 在"角色名称"输入框输入关键词
   - 点击"搜索"按钮
   - 检查表格数据是否按搜索条件过滤

3. **测试筛选功能**
   - 选择不同的状态选项
   - 检查表格数据是否按状态筛选

4. **测试重置功能**
   - 点击"重置"按钮
   - 检查搜索条件是否清空，数据是否恢复

5. **测试分页功能**
   - 在有搜索条件的情况下切换页码
   - 检查搜索条件是否保持

---

## 🔍 常见问题与解决方案

### 1. 后端问题

**问题1**: 查询参数绑定失败
```
解决方案：检查结构体标签是否正确，确保form标签与前端参数名一致
```

**问题2**: 模糊查询不生效
```
解决方案：确保使用LIKE操作符，并正确添加%通配符
```

**问题3**: 分页计算错误
```
解决方案：检查offset计算公式：offset = (page - 1) * pageSize
```

### 2. 前端问题

**问题1**: 搜索后分页异常
```
解决方案：搜索时将page重置为1
```

**问题2**: 表单重置不完全
```
解决方案：使用form.resetFields()清空表单，同时重置查询参数
```

**问题3**: 参数传递丢失
```
解决方案：检查useEffect依赖项，确保queryParams变化时重新请求
```

---

## 📚 学习要点总结

### 后端开发要点
1. **数据模型设计**：合理设计数据库表结构和Go结构体
2. **参数验证**：使用结构体标签进行参数绑定和验证
3. **查询构建**：灵活使用GORM的链式查询
4. **错误处理**：统一的错误响应格式
5. **分页逻辑**：正确计算offset和limit

### 前端开发要点
1. **类型定义**：使用TypeScript定义清晰的接口
2. **状态管理**：合理使用useState管理组件状态
3. **副作用处理**：正确使用useEffect监听状态变化
4. **表单处理**：使用Ant Design的Form组件
5. **用户体验**：加载状态、错误提示、操作反馈

### 开发流程
1. **需求分析** → **数据库设计** → **后端API** → **前端界面** → **测试验证**
2. **先后端后前端**：确保API稳定后再开发前端
3. **增量开发**：先实现基本功能，再逐步完善
4. **及时测试**：每完成一个功能点就进行测试

---

## 🎯 扩展练习

基于本文档的学习，可以尝试以下扩展功能：

1. **添加日期范围搜索**：按创建时间范围筛选角色
2. **添加排序功能**：支持按不同字段排序
3. **添加导出功能**：将搜索结果导出为Excel
4. **添加高级搜索**：支持更复杂的查询条件组合

每个扩展功能都可以按照本文档的流程进行开发，加深对前后端开发的理解。

---

## 📖 总结

本文档通过"角色模块添加搜索功能"这个具体例子，详细介绍了前后端开发的完整流程。从数据库设计到前端界面实现，每一步都有详细的代码示例和说明。

作为新手开发者，建议：
1. **按步骤实践**：严格按照文档步骤进行开发
2. **理解原理**：不仅要知道怎么做，更要理解为什么这么做
3. **多做练习**：通过扩展练习巩固所学知识
4. **及时总结**：每完成一个功能都要总结经验和教训

希望这份文档能帮助您快速掌握前后端开发技能！
