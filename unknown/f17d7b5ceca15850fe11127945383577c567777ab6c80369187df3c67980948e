-- V4__add_category_code.sql
-- 为商品分类表添加编码字段

USE `product_db`;

-- 检查并添加 code 字段（如果不存在）
SET @col_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                   WHERE TABLE_SCHEMA = 'product_db'
                   AND TABLE_NAME = 'category'
                   AND COLUMN_NAME = 'code');

SET @sql = IF(@col_exists = 0,
              'ALTER TABLE `category` ADD COLUMN `code` varchar(50) NOT NULL DEFAULT "" COMMENT "分类编码" AFTER `name`',
              'SELECT "code字段已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 清理测试数据
DELETE FROM `category` WHERE `name` = 'a' OR `name` = '' OR `name` IS NULL;

-- 为现有数据添加编码
UPDATE `category` SET `code` = 'FLOWER' WHERE `name` = '鲜花' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'GREEN_PLANT' WHERE `name` = '绿植' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'ROSE' WHERE `name` = '玫瑰' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'LILY' WHERE `name` = '百合' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'CARNATION' WHERE `name` = '康乃馨' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'POTTED' WHERE `name` = '盆栽' AND (`code` = '' OR `code` IS NULL);
UPDATE `category` SET `code` = 'FOLIAGE' WHERE `name` = '观叶植物' AND (`code` = '' OR `code` IS NULL);

-- 添加唯一索引（如果不存在）
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = 'product_db'
                     AND TABLE_NAME = 'category'
                     AND INDEX_NAME = 'uk_code');

SET @sql = IF(@index_exists = 0,
              'ALTER TABLE `category` ADD UNIQUE KEY `uk_code` (`code`)',
              'SELECT "uk_code索引已存在" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
