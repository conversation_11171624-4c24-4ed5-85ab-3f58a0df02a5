-- V2__init_data.sql
-- 初始化基础数据

USE `user_db`;

-- 插入角色数据
INSERT INTO `role` (`name`, `code`, `description`) VALUES
('系统管理员', 'ADMIN', '系统管理员角色'),
('拍卖师', 'AUCTIONEER', '拍卖师角色'),
('买家', 'BUYER', '买家角色'),
('质检员', 'QUALITY_INSPECTOR', '质检员角色');

-- 插入管理员用户
INSERT INTO `user` (`id`, `username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2a$10$YE0zwbpm/yqiVEONVs60XukNWLHce5fqA3wGOg5LZ4W103FwIbPum', '系统管理员', '13800000000', '<EMAIL>', 3, 1, 20250529, '2025-05-30 12:57:46'),
(2, 'testuser', '$2a$10$iHV7IE2HFvydSP2QnP2tg.qsquTEWbD4g3.QXIOeC.dyN9YXFKl.W', '测试用户', '13800138000', '<EMAIL>', 1, 1, 20250529, '2025-05-30 12:57:46'),
(3, 'testuser2', '$2a$10$iHV7IE2HFvydSP2QnP2tg.qsquTEWbD4g3.QXIOeC.dyN9YXFKl.W', '测试用户2', '13800138001', '<EMAIL>', 1, 1, 20250529, '2025-05-30 12:57:46'),
(4, 'testuser3', '$2a$10$iHV7IE2HFvydSP2QnP2tg.qsquTEWbD4g3.QXIOeC.dyN9YXFKl.W', '测试用户3', '13800138002', '<EMAIL>', 1, 1, 20250529, '2025-05-30 12:57:46'),
(5, 'seller01', '$2a$10$lt.rS2DapgdtJC08PS8qE.IA/bHTubLAgxG7b.XCexEq24QUsrQ8C', 'jimputh', '13810849217', '<EMAIL>', 2, 1, 20250529, '2025-05-30 12:57:46');

-- 插入管理员角色关联
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'admin' AND r.code = 'ADMIN';

USE `product_db`;

-- 插入商品类别数据
-- 注意：此版本不包含code字段，如需要分类编码功能，请执行以下迁移：
-- 1. V4__add_category_code.sql - 添加code字段
-- 2. V5__category_data_with_codes.sql - 插入包含编码的完整分类数据
--
-- ✅ 已完成：V4和V5迁移已执行，分类编码功能已可用
-- 可用的分类编码包括：FLOWER, GREEN_PLANT, DRIED_FLOWER, ACCESSORIES 等
-- 一级分类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('鲜花', NULL, 1, 1),
('绿植', NULL, 1, 2),
('干花', NULL, 1, 3),
('花束配件', NULL, 1, 4);

-- 二级分类 - 鲜花类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('玫瑰', 1, 2, 1),
('百合', 1, 2, 2),
('康乃馨', 1, 2, 3),
('郁金香', 1, 2, 4),
('向日葵', 1, 2, 5),
('满天星', 1, 2, 6),
('勿忘我', 1, 2, 7),
('桔梗', 1, 2, 8);

-- 二级分类 - 绿植类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('盆栽', 2, 2, 1),
('观叶植物', 2, 2, 2),
('多肉植物', 2, 2, 3),
('水培植物', 2, 2, 4),
('花卉盆栽', 2, 2, 5);

-- 二级分类 - 干花类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('干花花束', 3, 2, 1),
('干花装饰', 3, 2, 2),
('永生花', 3, 2, 3);

-- 二级分类 - 花束配件类
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('包装纸', 4, 2, 1),
('丝带', 4, 2, 2),
('花器', 4, 2, 3),
('装饰品', 4, 2, 4);