-- V5__category_data_with_codes.sql
-- 插入完整的商品分类数据（包含编码）
-- 注意：此文件应在V4迁移之后执行，确保category表已有code字段

USE `product_db`;

-- 清空现有分类数据（如果需要重新初始化）
-- DELETE FROM `category`;

-- 插入一级分类（包含编码）- 使用 INSERT IGNORE 避免重复
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('鲜花', 'FLOWER', NULL, 1, 1, 1),
('绿植', 'GREEN_PLANT', NULL, 1, 2, 1),
('干花', 'DRIED_FLOWER', NULL, 1, 3, 1),
('花束配件', 'ACCESSORIES', NULL, 1, 4, 1);

-- 插入二级分类 - 鲜花类
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('玫瑰', 'ROSE', 1, 2, 1, 1),
('百合', 'LILY', 1, 2, 2, 1),
('康乃馨', 'CARNATION', 1, 2, 3, 1),
('郁金香', 'TULIP', 1, 2, 4, 1),
('向日葵', 'SUNFLOWER', 1, 2, 5, 1),
('满天星', 'BABY_BREATH', 1, 2, 6, 1),
('勿忘我', 'FORGET_ME_NOT', 1, 2, 7, 1),
('桔梗', 'PLATYCODON', 1, 2, 8, 1),
('牡丹', 'PEONY', 1, 2, 9, 1),
('芍药', 'PAEONIA', 1, 2, 10, 1);

-- 插入二级分类 - 绿植类
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('盆栽', 'POTTED', 2, 2, 1, 1),
('观叶植物', 'FOLIAGE', 2, 2, 2, 1),
('多肉植物', 'SUCCULENT', 2, 2, 3, 1),
('水培植物', 'HYDROPONIC', 2, 2, 4, 1),
('花卉盆栽', 'FLOWERING_POT', 2, 2, 5, 1),
('室内植物', 'INDOOR_PLANT', 2, 2, 6, 1),
('办公植物', 'OFFICE_PLANT', 2, 2, 7, 1);

-- 插入二级分类 - 干花类
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('干花花束', 'DRIED_BOUQUET', 3, 2, 1, 1),
('干花装饰', 'DRIED_DECORATION', 3, 2, 2, 1),
('永生花', 'PRESERVED_FLOWER', 3, 2, 3, 1),
('干花材料', 'DRIED_MATERIAL', 3, 2, 4, 1);

-- 插入二级分类 - 花束配件类
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('包装纸', 'WRAPPING_PAPER', 4, 2, 1, 1),
('丝带', 'RIBBON', 4, 2, 2, 1),
('花器', 'VASE', 4, 2, 3, 1),
('装饰品', 'DECORATION', 4, 2, 4, 1),
('花泥', 'FLORAL_FOAM', 4, 2, 5, 1),
('花签', 'FLOWER_PICK', 4, 2, 6, 1);

-- 插入三级分类示例 - 玫瑰细分（parent_id指向玫瑰分类）
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('红玫瑰', 'RED_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 1, 1),
('白玫瑰', 'WHITE_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 2, 1),
('粉玫瑰', 'PINK_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 3, 1),
('黄玫瑰', 'YELLOW_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 4, 1),
('香槟玫瑰', 'CHAMPAGNE_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 5, 1),
('蓝玫瑰', 'BLUE_ROSE', (SELECT id FROM (SELECT id FROM category WHERE code = 'ROSE') AS t), 3, 6, 1);

-- 插入三级分类示例 - 百合细分（parent_id指向百合分类）
INSERT IGNORE INTO `category` (`name`, `code`, `parent_id`, `level`, `sort_order`, `status`) VALUES
('白百合', 'WHITE_LILY', (SELECT id FROM (SELECT id FROM category WHERE code = 'LILY') AS t), 3, 1, 1),
('粉百合', 'PINK_LILY', (SELECT id FROM (SELECT id FROM category WHERE code = 'LILY') AS t), 3, 2, 1),
('黄百合', 'YELLOW_LILY', (SELECT id FROM (SELECT id FROM category WHERE code = 'LILY') AS t), 3, 3, 1),
('香水百合', 'PERFUME_LILY', (SELECT id FROM (SELECT id FROM category WHERE code = 'LILY') AS t), 3, 4, 1);
